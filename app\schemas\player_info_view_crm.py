from typing import Optional, List
from pydantic import BaseModel
from datetime import datetime
import uuid

from app.schemas.contact import ContactShort
from app.schemas.organization import OrganizationShort
from app.schemas.user import UserShort
from app.schemas.notifications import NotificationsRead

class PlayerInfoViewCrmBase(BaseModel):
    """Base schema with core player info fields"""

    # Primary key
    id: uuid.UUID

    # Player info fields
    playerId: Optional[int] = None
    firstName: Optional[str] = None
    lastName: Optional[str] = None
    shortName: Optional[str] = None
    birthDate: Optional[str] = None
    weight: Optional[str] = None
    height: Optional[str] = None
    foot: Optional[str] = None
    passportArea_id: Optional[int] = None
    currentTeamId: Optional[int] = None
    agent_id: Optional[int] = None
    agent: Optional[str] = None
    contract_expiry: Optional[str] = None
    passportArea_name: Optional[str] = None
    passportArea_alpha3code: Optional[str] = None
    passportArea_alpha2code: Optional[str] = None
    team_name: Optional[str] = None
    divisionLevel: Optional[int] = None
    team_area_name: Optional[str] = None
    segment: Optional[str] = None
    category: Optional[str] = None
    smoothed_rating: Optional[float] = None
    tm_player_id: Optional[str] = None
    player_url: Optional[str] = None
    birthArea_name: Optional[str] = None
    birthArea_id: Optional[int] = None
    birthArea_alpha2code: Optional[str] = None
    birthArea_alpha3code: Optional[str] = None
    eu: Optional[bool] = None
    imageDataURL: Optional[str] = None
    primary_ws_position: Optional[str] = None
    secondary_ws_position: Optional[str] = None
    third_ws_position: Optional[str] = None
    current_value: Optional[float] = None
    gender: Optional[str] = None
    date_joined_current_team: Optional[str] = None

    # Player record fields
    created_at: Optional[datetime] = None
    last_updated: Optional[datetime] = None
    is_sensitive: Optional[bool] = None
    notes: Optional[str] = None
    control_stage: Optional[str] = None
    quality: Optional[int] = None
    position: Optional[List[str]] = None
    potential: Optional[int] = None
    club_asking_price: Optional[float] = None
    description: Optional[str] = None
    current_gross_salary: Optional[float] = None
    transfer_strategy: Optional[str] = None
    expected_net_salary: Optional[float] = None
    video_link: Optional[str] = None
    tags: Optional[List[str]]

    class Config:
        orm_mode = True
        use_cache = True


class PlayerInfoViewCrm(PlayerInfoViewCrmBase):
    """Full schema with relationships"""

    # Relationships
    organization: Optional[OrganizationShort] = None
    creator: Optional[UserShort] = None
    assigned_contacts: Optional[List[ContactShort]] = None
    player_notifications: Optional[NotificationsRead] = None

    # Priority status (computed field)
    is_priority: Optional[bool] = False

    class Config:
        orm_mode = True
        use_cache = True


class PlayerInfoViewCrmShort(BaseModel):
    """Short version without relationships for performance"""
    playerId: int
    firstName: str
    lastName: str
    shortName: Optional[str] = None
    birthDate: Optional[str]
    team_name: Optional[str] = None
    currentTeamId: Optional[int] = None
    passport: Optional[str] = None
    tm_link: Optional[str] = None
    is_priority: Optional[bool] = False

    class Config:
        orm_mode = True
        use_cache = True

class PlayerInfoViewCrmLookup(BaseModel):
    playerId: Optional[int] = None
    team_name: Optional[str] = None
    firstName: Optional[str] = None
    lastName: Optional[str] = None
    currentTeamId: Optional[int] = None
    position: Optional[List[str]] = None
    id: uuid.UUID = None
    class Config:
        orm_mode = True
        use_cache = True
