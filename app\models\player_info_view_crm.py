from sqlalchemy import String, UUID, DateTime
from sqlalchemy.orm import Mapped, mapped_column, relationship, foreign
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.dialects.postgresql import ARRAY
from app.db.base_class import Base
from app.config import settings
from typing import Optional, List
from datetime import datetime
import uuid


class PlayerInfoViewCrm(Base):
    __tablename__ = settings.PLAYER_INFO_VIEW_CRM
    __table_args__ = {"schema": "wyscout"}

    # Primary key (from player_records.id)
    id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), primary_key=True)

    # Pass-through keys for relationships
    organization_id: Mapped[Optional[uuid.UUID]] = mapped_column(UUID(as_uuid=True))
    created_by: Mapped[Optional[uuid.UUID]] = mapped_column(UUID(as_uuid=True))

    # Player info fields
    playerId: Mapped[Optional[int]]
    firstName: Mapped[Optional[str]]
    lastName: Mapped[Optional[str]]
    shortName: Mapped[Optional[str]]
    birthDate: Mapped[Optional[str]]
    weight: Mapped[Optional[str]]
    height: Mapped[Optional[str]]
    foot: Mapped[Optional[str]]
    passportArea_id: Mapped[Optional[int]]
    currentTeamId: Mapped[Optional[int]]
    agent: Mapped[Optional[str]]
    agent_id: Mapped[Optional[int]]
    contract_expiry: Mapped[Optional[str]]
    passportArea_name: Mapped[Optional[str]]
    passportArea_alpha3code: Mapped[Optional[str]]
    passportArea_alpha2code: Mapped[Optional[str]]
    team_name: Mapped[Optional[str]]
    divisionLevel: Mapped[Optional[int]]
    team_area_name: Mapped[Optional[str]]
    segment: Mapped[Optional[str]]
    category: Mapped[Optional[str]]
    smoothed_rating: Mapped[Optional[float]]
    tm_player_id: Mapped[Optional[str]]
    player_url: Mapped[Optional[str]]
    birthArea_name: Mapped[Optional[str]]
    birthArea_id: Mapped[Optional[int]]
    birthArea_alpha2code: Mapped[Optional[str]]
    birthArea_alpha3code: Mapped[Optional[str]]
    eu: Mapped[Optional[bool]]
    imageDataURL: Mapped[Optional[str]]
    primary_ws_position: Mapped[Optional[str]]
    secondary_ws_position: Mapped[Optional[str]]
    third_ws_position: Mapped[Optional[str]]
    current_value: Mapped[Optional[float]]
    gender: Mapped[Optional[str]]
    date_joined_current_team: Mapped[Optional[str]]

    # Player record fields
    created_at: Mapped[Optional[datetime]]
    last_updated: Mapped[Optional[datetime]]
    is_sensitive: Mapped[Optional[bool]]
    notes: Mapped[Optional[str]]
    control_stage: Mapped[Optional[str]]
    quality: Mapped[Optional[int]]
    position: Mapped[Optional[list[str]]] = mapped_column(ARRAY(String))
    potential: Mapped[Optional[int]]
    club_asking_price: Mapped[Optional[float]]
    description: Mapped[Optional[str]]
    current_gross_salary: Mapped[Optional[float]]
    transfer_strategy: Mapped[Optional[str]]
    expected_net_salary: Mapped[Optional[float]]
    video_link: Mapped[Optional[str]]
    tags: Mapped[Optional[list[str]]] = mapped_column(ARRAY(String))

    # Relationships (view-only) - using foreign() annotation since this is a view
    organization = relationship(
        "Organization",
        primaryjoin="foreign(PlayerInfoViewCrm.organization_id)==Organization.id",
        viewonly=True,
        lazy="selectin",
    )

    creator = relationship(
        "User",
        primaryjoin="foreign(PlayerInfoViewCrm.created_by)==User.id",
        viewonly=True,
        lazy="selectin",
    )

    # Relationship to assigned contacts through AssignedToRecord
    assigned_to_record = relationship(
        "AssignedToRecord",
        primaryjoin="foreign(PlayerInfoViewCrm.id)==AssignedToRecord.player_id",
        viewonly=True,
        lazy="selectin",
        uselist=True,  # Explicitly specify this should return a list
    )

    # Priority relationship (view-only, will be handled in CRUD layer)
    priority_players = relationship(
        "PriorityPlayers",
        primaryjoin="foreign(PlayerInfoViewCrm.id)==PriorityPlayers.player_record_id",
        viewonly=True,
        lazy="selectin",
    )

    player_notifications = relationship(
        "NotificationSettings",
        primaryjoin="foreign(PlayerInfoViewCrm.id)==NotificationSettings.player_id",
        viewonly=True,
        lazy="selectin",
    )

    @property
    def assigned_contacts(self):
        """Extract contacts from assigned_to_record relationship."""
        if not self.assigned_to_record:
            return []

        # assigned_to_record should be a list due to uselist=True
        return [record.contact for record in self.assigned_to_record if record.contact]
