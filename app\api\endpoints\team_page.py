from typing import Any, List, Dict, Optional

from fastapi import APIRouter, Depends, Query
from sqlalchemy.orm import Session

from app.schemas.team_page import PlayerFromTeam
from app import models
from app.api import deps
from sqlalchemy import text
from app.config import settings

router = APIRouter()


@router.get("/", response_model=Dict[str, Any])
def read_teams(
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
    page: int = Query(0, alias="offset", ge=0),
    limit: int = Query(10, le=100),
    search: Optional[str] = Query(None, alias="search"),
    area_names: Optional[List[str]] = Query(
        None, alias="area_name"
    ),  # Multiple area names
    division_levels: Optional[List[int]] = Query(
        None, alias="divisionLevel"
    ),  # Multiple divisions
    category: Optional[str] = Query(
        None, alias="category", regex="^(youth|default)$"
    ),  # Filter by category
    is_favourite: Optional[bool] = Query(
        None, alias="is_favourite"
    ),  # Filter by favourite status
    all_requests_min: Optional[int] = Query(None, alias="all_requests_min"),
    all_requests_max: Optional[int] = Query(None, alias="all_requests_max"),
    open_requests_min: Optional[int] = Query(None, alias="open_requests_min"),
    open_requests_max: Optional[int] = Query(None, alias="open_requests_max"),
    all_activities_min: Optional[int] = Query(None, alias="all_activities_min"),
    all_activities_max: Optional[int] = Query(None, alias="all_activities_max"),
    open_activities_min: Optional[int] = Query(None, alias="open_activities_min"),
    open_activities_max: Optional[int] = Query(None, alias="open_activities_max"),
    signed_players_min: Optional[int] = Query(None, alias="signed_players_min"),
    signed_players_max: Optional[int] = Query(None, alias="signed_players_max"),
    mandate_players_min: Optional[int] = Query(None, alias="mandate_players_min"),
    mandate_players_max: Optional[int] = Query(None, alias="mandate_players_max"),
) -> Any:
    """
    Retrieve paginated Teams with free text search on officialName,
    exact match filtering on area_name, and filtering on divisionLevel.

    Filters available:
    - search: Free text search on officialName
    - area_name: Filter by area names (multiple values supported)
    - divisionLevel: Filter by division levels (multiple values supported)
    - category: Filter by category (youth/default)
    - is_favourite: Filter by favourite status (true=favourites only, false=non-favourites only, null=all teams)
    - Various count filters for requests, activities, signed players, and mandate players
    """
    skip = page * limit
    # Build base WHERE clause
    where_clauses = ["ti.area_name IS NOT NULL"]
    params = {
        "limit": limit,
        "skip": skip,
        "org_id": current_user.organization_id,
        "user_id": current_user.id,
    }

    # Search filter (text search on officialName)
    if search:
        where_clauses.append('unaccent(ti."officialName") ILIKE unaccent(:search)')
        params["search"] = f"%{search}%"  # Wildcard for partial match

    # Exact match filtering for multiple area_names
    if area_names:
        where_clauses.append("ti.area_name = ANY(:area_names)")
        params["area_names"] = area_names

    # Filtering for multiple divisionLevels
    if division_levels:
        where_clauses.append('ti."divisionLevel" = ANY(:division_levels)')
        params["division_levels"] = division_levels

    # Filtering by category (only 'youth' or 'default')
    if category:
        where_clauses.append("ti.category = :category")
        params["category"] = category

    # Filtering by favourite status
    if is_favourite is not None:
        if is_favourite:
            # Only favourite teams (ft."teamId" IS NOT NULL)
            where_clauses.append('ft."teamId" IS NOT NULL')
        else:
            # Only non-favourite teams (ft."teamId" IS NULL)
            where_clauses.append('ft."teamId" IS NULL')

    # Filters for All Requests
    if all_requests_min is not None:
        where_clauses.append("COALESCE(tr.all_requests, 0) >= :all_requests_min")
        params["all_requests_min"] = all_requests_min
    if all_requests_max is not None:
        where_clauses.append("COALESCE(tr.all_requests, 0) <= :all_requests_max")
        params["all_requests_max"] = all_requests_max

    # Filters for Open Requests
    if open_requests_min is not None:
        where_clauses.append("COALESCE(tr.open_requests, 0) >= :open_requests_min")
        params["open_requests_min"] = open_requests_min
    if open_requests_max is not None:
        where_clauses.append("COALESCE(tr.open_requests, 0) <= :open_requests_max")
        params["open_requests_max"] = open_requests_max

    # Filters for All Activities
    if all_activities_min is not None:
        where_clauses.append("COALESCE(ta.all_activities, 0) >= :all_activities_min")
        params["all_activities_min"] = all_activities_min
    if all_activities_max is not None:
        where_clauses.append("COALESCE(ta.all_activities, 0) <= :all_activities_max")
        params["all_activities_max"] = all_activities_max

    # Filters for Open Activities
    if open_activities_min is not None:
        where_clauses.append("COALESCE(ta.open_activities, 0) >= :open_activities_min")
        params["open_activities_min"] = open_activities_min
    if open_activities_max is not None:
        where_clauses.append("COALESCE(ta.open_activities, 0) <= :open_activities_max")
        params["open_activities_max"] = open_activities_max

    # Filters for Signed Players
    if signed_players_min is not None:
        where_clauses.append("COALESCE(sp.signed_players, 0) >= :signed_players_min")
        params["signed_players_min"] = signed_players_min
    if signed_players_max is not None:
        where_clauses.append("COALESCE(sp.signed_players, 0) <= :signed_players_max")
        params["signed_players_max"] = signed_players_max

    # Filters for Mandate Players
    if mandate_players_min is not None:
        where_clauses.append("COALESCE(mp.mandate_players, 0) >= :mandate_players_min")
        params["mandate_players_min"] = mandate_players_min
    if mandate_players_max is not None:
        where_clauses.append("COALESCE(mp.mandate_players, 0) <= :mandate_players_max")
        params["mandate_players_max"] = mandate_players_max

    where_clause = "WHERE " + " AND ".join(where_clauses)

    count_query = text(
        f"""
            WITH team_requests AS (
            SELECT 
                "teamId", 
                COUNT(DISTINCT id) AS all_requests,
                COUNT(DISTINCT CASE WHEN status != 'closed' THEN id END) AS open_requests
            FROM {settings.PG_SCHEMA}.team_requests
            WHERE organization_id = :org_id
            GROUP BY "teamId"
        ),
        team_activities AS (
            SELECT 
                "teamId", 
                COUNT(DISTINCT id) AS all_activities,
                COUNT(DISTINCT CASE WHEN stage NOT IN ('done', 'no interest') THEN id END) AS open_activities
            FROM {settings.PG_SCHEMA}.activity
            WHERE organization_id = :org_id
            GROUP BY "teamId"
        ),
        signed_players AS (
            SELECT 
                pi2_signed."currentTeamId" as "teamId", 
                COUNT(DISTINCT pi2_signed."playerId") AS signed_players
            FROM wyscout.{settings.PLAYER_INFO_VIEW_CRM} pi2_signed
                where pi2_signed.organization_id = :org_id
                AND pi2_signed.control_stage = 'signed'
            GROUP BY pi2_signed."currentTeamId"
        ),
        mandate_players AS (
            SELECT 
                pi2_mandate."currentTeamId" as "teamId",
                COUNT(DISTINCT pi2_mandate."playerId") AS mandate_players
            FROM wyscout.{settings.PLAYER_INFO_VIEW_CRM} pi2_mandate
                where pi2_mandate.organization_id = :org_id
                AND pi2_mandate.control_stage IN ('mandate_on_demand', 'mandate')
            GROUP BY pi2_mandate."currentTeamId"
        )
        SELECT COUNT(*)
        FROM wyscout.top_teams ti
        LEFT JOIN team_requests tr
            ON tr."teamId" = ti."teamId"
        LEFT JOIN team_activities ta
            ON ta."teamId" = ti."teamId"
        LEFT JOIN signed_players sp
            ON sp."teamId" = ti."teamId"
        LEFT JOIN mandate_players mp
            ON mp."teamId" = ti."teamId"
        LEFT JOIN {settings.PG_SCHEMA}.favourite_teams ft
            ON ft."teamId" = ti."teamId" and ft.user_id = :user_id
        {where_clause}
        """
    )
    total_count = db.execute(count_query, params).scalar()

    # Get paginated teams
    query = text(
        f"""
        WITH team_requests AS (
            SELECT 
                "teamId", 
                COUNT(DISTINCT id) AS all_requests,
                COUNT(DISTINCT CASE WHEN status != 'closed' THEN id END) AS open_requests
            FROM {settings.PG_SCHEMA}.team_requests
            WHERE organization_id = :org_id
            GROUP BY "teamId"
        ),
        team_activities AS (
            SELECT 
                "teamId", 
                COUNT(DISTINCT id) AS all_activities,
                COUNT(DISTINCT CASE WHEN stage NOT IN ('done', 'no interest') THEN id END) AS open_activities
            FROM {settings.PG_SCHEMA}.activity
            WHERE organization_id = :org_id
            GROUP BY "teamId"
        ),
        signed_players AS (
            SELECT 
                pi2_signed."currentTeamId" as "teamId", 
                COUNT(DISTINCT pi2_signed."playerId") AS signed_players
            FROM wyscout.{settings.PLAYER_INFO_VIEW_CRM} pi2_signed
                where pi2_signed.organization_id = :org_id
                AND pi2_signed.control_stage = 'signed'
            GROUP BY pi2_signed."currentTeamId"
        ),
        mandate_players AS (
            SELECT 
                pi2_mandate."currentTeamId" as "teamId",
                COUNT(DISTINCT pi2_mandate."playerId") AS mandate_players
            FROM wyscout.{settings.PLAYER_INFO_VIEW_CRM} pi2_mandate
                where pi2_mandate.organization_id = :org_id
                AND pi2_mandate.control_stage IN ('mandate_on_demand', 'mandate')
            GROUP BY pi2_mandate."currentTeamId"
        )
        SELECT
            ti."officialName",
            ti.area_name,
            ti."divisionLevel",
            ti.league_name,
            ti.category,
            ti."teamId",
            ti.players_count,
            ti.type,
            COALESCE(tr.all_requests, 0) AS all_requests,
            COALESCE(tr.open_requests, 0) AS open_requests,
            COALESCE(ta.all_activities, 0) AS all_activities,
            COALESCE(ta.open_activities, 0) AS open_activities,
            COALESCE(sp.signed_players, 0) AS signed_players,
            COALESCE(mp.mandate_players, 0) AS mandate_players,
            CASE WHEN ft."teamId" IS NOT NULL THEN true ELSE false END AS is_favourite
        FROM wyscout.top_teams ti
        LEFT JOIN team_requests tr 
            ON tr."teamId" = ti."teamId"
        LEFT JOIN team_activities ta 
            ON ta."teamId" = ti."teamId"
        LEFT JOIN signed_players sp 
            ON sp."teamId" = ti."teamId"
        LEFT JOIN mandate_players mp 
            ON mp."teamId" = ti."teamId"
        LEFT JOIN {settings.PG_SCHEMA}.favourite_teams ft
            ON ft."teamId" = ti."teamId" and ft.user_id = :user_id
        {where_clause}
        ORDER BY
            is_favourite DESC,
            ti.rating DESC,
            ti."teamId" ASC
        LIMIT :limit OFFSET :skip;
        """
    )

    teams = db.execute(query, params).mappings().all()

    return {
        "total_count": total_count,
        "total_pages": (total_count + limit - 1) // limit,
        "limit": limit,
        "offset": skip,
        "data": teams,
    }


@router.get("/squad/{teamId}", response_model=List[PlayerFromTeam])
def read_players_from_a_team(
    teamId: str,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve all players from a team.
    """
    query = text(
        f"""
        SELECT *
        FROM (
            SELECT 
                pi2."playerId", 
                pi2."firstName", 
                pi2."lastName", 
                pi2."birthDate", 
                pi2."passportArea_name" as passport, 
                pi2.height, 
                pi2.weight,
                pi2.primary_ws_position, 
                pi2.secondary_ws_position, 
                pi2.current_value, 
                pi2.contract_expiry, 
                pi2.agent,
                pi2.player_url,
                CASE 
                    WHEN pr."playerId" IS NOT NULL THEN TRUE 
                    ELSE FALSE 
                END AS mine
            FROM wyscout.{settings.PLAYER_INFO_VIEW_SYSTEM} pi2
            left join {settings.PG_SCHEMA}.player_records pr on pr."playerId" = pi2."playerId"
            and pr.organization_id = :org_id
            WHERE pi2."currentTeamId" = :teamId
            UNION
            SELECT 
                pi2."playerId", 
                pi2."firstName", 
                pi2."lastName", 
                pi2."birthDate", 
                pi2."passportArea_name" as passport, 
                pi2.height, 
                pi2.weight,
                pi2.primary_ws_position, 
                pi2.secondary_ws_position, 
                pi2.current_value, 
                pi2.contract_expiry, 
                pi2.agent,
                pi2.player_url,
                CASE 
                    WHEN pr."playerId" IS NOT NULL THEN TRUE 
                    ELSE FALSE 
                END AS mine
            FROM wyscout.{settings.PLAYER_INFO_VIEW_SYSTEM} pi2
            join wyscout.players p on p."playerId" = pi2."playerId"
            left join {settings.PG_SCHEMA}.player_records pr on pr."playerId" = pi2."playerId"
            and pr.organization_id = :org_id
            WHERE p."currentNationalTeamId" = :teamId
        ) AS players
        ORDER BY 
            CASE primary_ws_position
                WHEN 'gk'    THEN  1
                WHEN 'rb'    THEN 2
                WHEN 'rb5'   THEN 3
                WHEN 'rwb'   THEN 4
                WHEN 'cb'    THEN  5
                WHEN 'rcb'   THEN  6
                WHEN 'rcb3'  THEN  7
                WHEN 'lcb'   THEN  8
                WHEN 'lcb3'  THEN  9
                WHEN 'lb'    THEN  10
                WHEN 'lb5'   THEN  11
                WHEN 'lwb'   THEN  12
                WHEN 'dmf'   THEN 13
                WHEN 'ldmf'  THEN 14
                WHEN 'rdmf'  THEN 15
                WHEN 'lcmf'  THEN 16
                WHEN 'lcmf3' THEN 17
                WHEN 'rcmf'  THEN 18
                WHEN 'rcmf3' THEN 19
                WHEN 'lamf'  THEN 20
                WHEN 'amf'   THEN 21
                WHEN 'ramf'  THEN 22
                WHEN 'lw'    THEN 23
                WHEN 'lwf'   THEN 24
                WHEN 'rw'    THEN 25
                WHEN 'rwf'   THEN 26
                WHEN 'ss'    THEN 27
                WHEN 'cf'    THEN 28
                ELSE 999  -- Put unknown/other positions at the end
            END;
        """
    )
    players = (
        db.execute(query, {"teamId": teamId, "org_id": current_user.organization_id})
        .mappings()
        .all()
    )

    return players


@router.get("/staff/{teamId}")
def read_staff_data_for_a_team(
    teamId: str,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve key staff members for a specific team.
    """
    query = text(
        f"""
        select si.* from transfermarkt.tm_to_ws_team_ids ttwti
        join transfermarkt.staff_info si on si.team_id = ttwti.transfermarkt_team_id
        where ttwti."teamId" = :teamId and role in ('head_coach', 'assistant_coach', 'sporting_director', 'coaching_staff', 'club_executives')
        and si.appointed is not null
        """
    )
    staffs = db.execute(query, {"teamId": teamId}).mappings().all()

    return staffs


@router.get("/league_standings/{teamId}")
def read_league_standings_for_a_team(
    teamId: str,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve league standings for a specific team.
    """
    query = text(
        f"""
    select ls."totalPoints", ls."totalPoints", ls.placement, ls."totalGoalsFor", ls."totalGoalsAgainst", ti.name, ti."teamId", ls."totalPlayed" from wyscout.leagues_standings ls
     join wyscout.seasons s on s."seasonId" = ls."seasonId" and s.ongoing 
     join wyscout.competition_teams ct on ct."competitionId" = s."competitionId" 
     join wyscout.team_info2 ti on ti."teamId" = ls."teamId" 
     where ct."teamId" = :teamId
     order by ls.placement
        """
    )
    league_standings = db.execute(query, {"teamId": teamId}).mappings().all()

    return league_standings
