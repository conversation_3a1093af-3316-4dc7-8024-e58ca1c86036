from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException, Request
from sqlalchemy.orm import Session

from app import crud, models
from app.api import deps
from app.schemas.priority_players import (
    PriorityPlayerCreate,
    PriorityPlayerRead,
    PriorityPlayerDelete,
)
from app.utils.audit_decorator import audit_action

router = APIRouter()


@router.post("/", response_model=PriorityPlayerRead)
@audit_action(action_type="create", record_type="priority_player")
def create_priority_player(
    *,
    db: Session = Depends(deps.get_db),
    priority_player_in: PriorityPlayerCreate,
    request: Request,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create a new priority player for the current user.
    """
    # Check if the player record exists and belongs to the user's organization
    player_record = crud.player_record.get_by_org(
        db=db, id=priority_player_in.player_record_id, org_id=current_user.organization_id
    )
    if not player_record:
        raise HTTPException(
            status_code=404, 
            detail="Player record not found or not accessible"
        )
    
    priority_player = crud.priority_players.create_with_user(
        db=db, obj_in=priority_player_in, user_id=current_user.id
    )
    return priority_player


@router.get("/", response_model=List[PriorityPlayerRead])
def read_priority_players(
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve priority players for the current user.
    """
    priority_players = crud.priority_players.get_by_user(
        db=db, user_id=current_user.id
    )
    return priority_players


@router.delete("/")
@audit_action(action_type="delete", record_type="priority_player",)
def delete_priority_player(
    *,
    db: Session = Depends(deps.get_db),
    priority_player_delete: PriorityPlayerDelete,
    request: Request,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Remove a priority player for the current user.
    """
    success = crud.priority_players.delete_by_user_and_player(
        db=db, 
        user_id=current_user.id, 
        player_record_id=priority_player_delete.player_record_id
    )
    if not success:
        raise HTTPException(
            status_code=404, 
            detail="Priority player not found"
        )
    return {"message": "Priority player removed successfully"}


@router.get("/check/{player_record_id}")
def check_priority_player(
    *,
    db: Session = Depends(deps.get_db),
    player_record_id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Check if a player is marked as priority for the current user.
    """
    is_priority = crud.priority_players.is_priority_for_user(
        db=db, user_id=current_user.id, player_record_id=player_record_id
    )
    return {"is_priority": is_priority}
