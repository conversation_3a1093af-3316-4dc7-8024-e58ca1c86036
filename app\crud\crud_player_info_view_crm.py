from typing import Sequence, Dict, List, Optional, Any
from sqlalchemy import select, case, and_, or_, func, desc, asc
from sqlalchemy.orm import Session, selectinload
from fastapi import HTTPException
import uuid
from app.crud.crud_base import CRUDBase
from app import models
from app.models.player_info_view_crm import PlayerInfoViewCrm
from app.models.assigned_to_record import AssignedToRecord


class PlayerInfoViewCrmService(CRUDBase[models.PlayerInfoViewCrm, None, None]):
    """
    Generic read service for PlayerInfoViewCrm that handles filters, sorting, and pagination.
    All read operations funnel through this service.
    """

    def __init__(self):
        self.model = PlayerInfoViewCrm

    def list_players(
        self,
        session: Session,
        filters: Optional[Dict[str, Any]] = None,
        sort: Optional[List[str]] = None,
        limit: Optional[int] = None,
        offset: int = 0,
        user_id: Optional[uuid.UUID] = None,
        org_id: Optional[uuid.UUID] = None,
        can_access_sensitive: bool = False,
    ) -> Sequence[PlayerInfoViewCrm]:
        """
        List players with filters, sorting, and pagination.

        Args:
            session: Database session
            filters: Dictionary of filters to apply
            sort: List of sort fields (prefix with '-' for descending)
            limit: Maximum number of results
            offset: Number of results to skip
            user_id: User ID for priority calculation
            org_id: Organization ID for filtering
            can_access_sensitive: Whether user can access sensitive data

        Returns:
            Sequence of PlayerInfoViewCrm objects
        """
        if user_id:
            # Include priority status when user_id is provided
            query = session.query(
                self.model,
                case((models.PriorityPlayers.id.isnot(None), True), else_=False).label(
                    "is_priority"
                ),
            ).outerjoin(
                models.PriorityPlayers,
                and_(
                    models.PriorityPlayers.player_record_id == self.model.id,
                    models.PriorityPlayers.user_id == user_id,
                ),
            )
        else:
            query = session.query(self.model)

        # Add relationship loading
        query = query.options(
            selectinload(self.model.organization),
            selectinload(self.model.creator),
            selectinload(self.model.player_notifications),
            selectinload(self.model.assigned_to_record).selectinload(
                AssignedToRecord.contact
            ),
        )

        # Apply organization filter
        if org_id:
            query = query.filter(self.model.organization_id == org_id)

        # Apply sensitive data filter
        if not can_access_sensitive:
            query = query.filter(self.model.is_sensitive != True)

        # Apply custom filters
        query = self._apply_filters(query, filters or {})

        # Apply sorting
        query = self._apply_sorting(query, sort or [])

        # Apply pagination (only if limit is specified)
        if limit is not None:
            query = query.limit(limit).offset(offset)
        query = query.order_by(self.model.last_updated.desc())

        results = query.all()

        # If user_id was provided, results are tuples (model, is_priority)
        # We need to set the is_priority attribute on the model instances
        if user_id:
            processed_results = []
            for model_instance, is_priority in results:
                # Set the is_priority attribute on the model instance
                setattr(model_instance, "is_priority", is_priority)
                processed_results.append(model_instance)
            return processed_results

        return results

    def get_player_by_id(
        self,
        session: Session,
        player_id: uuid.UUID,
        user_id: Optional[uuid.UUID] = None,
        org_id: Optional[uuid.UUID] = None,
        can_access_sensitive: bool = False,
    ) -> Optional[PlayerInfoViewCrm]:
        """
        Get a single player by ID.

        Args:
            session: Database session
            player_id: Player record ID
            user_id: User ID for priority calculation
            org_id: Organization ID for filtering
            can_access_sensitive: Whether user can access sensitive data

        Returns:
            PlayerInfoViewCrm object or None
        """
        if user_id:
            query = session.query(
                self.model,
                case((models.PriorityPlayers.id.isnot(None), True), else_=False).label(
                    "is_priority"
                ),
            ).outerjoin(
                models.PriorityPlayers,
                and_(
                    models.PriorityPlayers.player_record_id == self.model.id,
                    models.PriorityPlayers.user_id == user_id,
                ),
            )
        else:
            query = session.query(self.model)

        # Add relationship loading
        query = query.options(
            selectinload(self.model.organization),
            selectinload(self.model.creator),
            selectinload(self.model.assigned_to_record).selectinload(
                AssignedToRecord.contact
            ),
        )

        # Apply filters
        query = query.filter(self.model.id == player_id)

        if org_id:
            query = query.filter(self.model.organization_id == org_id)

        if not can_access_sensitive:
            query = query.filter(self.model.is_sensitive != True)

        result = query.first()

        # If user_id was provided, result is a tuple (model, is_priority)
        if user_id and result:
            model_instance, is_priority = result
            setattr(model_instance, "is_priority", is_priority)
            return model_instance

        return result

    def count_players(
        self,
        session: Session,
        filters: Optional[Dict[str, Any]] = None,
        org_id: Optional[uuid.UUID] = None,
        can_access_sensitive: bool = False,
    ) -> int:
        """
        Count players matching the given filters.

        Args:
            session: Database session
            filters: Dictionary of filters to apply
            org_id: Organization ID for filtering
            can_access_sensitive: Whether user can access sensitive data

        Returns:
            Count of matching players
        """
        query = session.query(func.count(self.model.id))

        # Apply organization filter
        if org_id:
            query = query.filter(self.model.organization_id == org_id)

        # Apply sensitive data filter
        if not can_access_sensitive:
            query = query.filter(self.model.is_sensitive != True)

        # Apply custom filters
        query = self._apply_filters(query, filters or {})

        return query.scalar()

    def _apply_filters(self, query, filters: Dict[str, Any]):
        """Apply filters to the query."""
        for key, value in filters.items():
            if value is None:
                continue

            column = getattr(self.model, key, None)
            if column is None:
                continue

            if isinstance(value, list):
                # Handle array/list filters
                if key == "control_stage":
                    query = query.filter(column.in_(value))
                elif key == "position":
                    # Handle position array overlap
                    query = query.filter(column.overlap(value))
                else:
                    query = query.filter(column.in_(value))
            elif isinstance(value, str) and key in [
                "first_name",
                "last_name",
                "team_name",
                "description",
            ]:
                # Handle text search with ILIKE
                query = query.filter(column.ilike(f"%{value}%"))
            else:
                # Handle exact matches
                query = query.filter(column == value)

        return query

    def _apply_sorting(self, query, sort_fields: List[str]):
        """Apply sorting to the query."""
        for field in sort_fields:
            desc_order = field.startswith("-")
            field_name = field[1:] if desc_order else field

            column = getattr(self.model, field_name, None)
            if column is not None:
                if desc_order:
                    query = query.order_by(desc(column))
                else:
                    query = query.order_by(asc(column))

        return query

    def search_players(
        self,
        session: Session,
        search_term: str,
        user_id: Optional[uuid.UUID] = None,
        org_id: Optional[uuid.UUID] = None,
        can_access_sensitive: bool = False,
        limit: Optional[int] = None,
        offset: int = 0,
    ) -> Sequence[PlayerInfoViewCrm]:
        """
        Search players across multiple fields.

        Args:
            session: Database session
            search_term: Search term to look for
            user_id: User ID for priority calculation
            org_id: Organization ID for filtering
            can_access_sensitive: Whether user can access sensitive data
            limit: Maximum number of results
            offset: Number of results to skip

        Returns:
            Sequence of PlayerInfoViewCrm objects
        """
        search_filter = or_(
            self.model.first_name.ilike(f"%{search_term}%"),
            self.model.last_name.ilike(f"%{search_term}%"),
            self.model.team_name.ilike(f"%{search_term}%"),
            self.model.description.ilike(f"%{search_term}%"),
            self.model.agency.ilike(f"%{search_term}%"),
        )

        if user_id:
            query = session.query(
                self.model,
                case((models.PriorityPlayers.id.isnot(None), True), else_=False).label(
                    "is_priority"
                ),
            ).outerjoin(
                models.PriorityPlayers,
                and_(
                    models.PriorityPlayers.player_record_id == self.model.id,
                    models.PriorityPlayers.user_id == user_id,
                ),
            )
        else:
            query = session.query(self.model)

        # Add relationship loading
        query = query.options(
            selectinload(self.model.organization),
            selectinload(self.model.creator),
            selectinload(self.model.assigned_to_record).selectinload(
                AssignedToRecord.contact
            ),
        )

        # Apply search filter
        query = query.filter(search_filter)

        # Apply organization filter
        if org_id:
            query = query.filter(self.model.organization_id == org_id)

        # Apply sensitive data filter
        if not can_access_sensitive:
            query = query.filter(self.model.is_sensitive != True)

        # Apply pagination (only if limit is specified)
        if limit is not None:
            query = query.limit(limit).offset(offset)

        results = query.all()

        # If user_id was provided, results are tuples (model, is_priority)
        if user_id:
            processed_results = []
            for model_instance, is_priority in results:
                setattr(model_instance, "is_priority", is_priority)
                processed_results.append(model_instance)
            return processed_results

        return results


# Create a singleton instance
player_info_view_crm_service = PlayerInfoViewCrmService()
