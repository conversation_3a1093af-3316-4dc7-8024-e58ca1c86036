from typing import Any, List
from fastapi import APIRouter, Depends, BackgroundTasks, Request
from sqlalchemy.orm import Session
from app.schemas.community_deal import CommunityDeal, CommunityDealUpdate
from app.schemas.comment import CommentCreate
from app.schemas.player_record import Player<PERSON><PERSON><PERSON><PERSON><PERSON>
from app import crud, models
from app.api import deps, utils
from app.utils import mail, positionMappingWS
from fastapi import HTTPException
from app.api.endpoints.platform_notifications import create_in_app_notification
from app.api.endpoints.message_subscription import read_subscriptions_email
from app.api.endpoints.platform_notifications import create_in_app_notification
from app.utils.audit_decorator import audit_action

router = APIRouter()


@router.get("/", response_model=List[CommunityDeal])
def read_all_community_deals(
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve all community deals.
    """
    return crud.community_deal.get_all_w_org(
        db, current_user.organization_id, utils.can_access_sensitive(current_user)
    )


@router.put("/{id}")
@audit_action(
    action_type="update",
    record_type="community_deal",
    get_original_object=lambda kwargs: crud.community_deal.get_by_org(
        kwargs['db'], kwargs['id'], kwargs['current_user'].organization_id
    )
)
def update_feedback(
    id: str,
    community_deal_in: CommunityDealUpdate,
    background_tasks: BackgroundTasks,
    request: Request,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Update community feedback.
    """
    community_deal = crud.community_deal.get_by_org(db=db, id=id, org_id=current_user.organization_id)
    if community_deal.feedback:
        raise HTTPException(
            status_code=403, detail="You can't change an already given feedback"
        )

    utils.check_modify(community_deal, current_user)
    crud.community_deal.update(db, db_obj=community_deal, obj_in=community_deal_in)

    linked_community_deal = crud.community_deal.get_oposite_deal(db, community_deal.id)

    if community_deal_in.feedback == "i_am_interested":
        linked_data = community_deal_in.dict()
        linked_data["email_proposed_to"] = current_user.email
        linked_update = CommunityDealUpdate(**linked_data)
    else:
        linked_update = community_deal_in  # fallback if nothing special to add
    crud.community_deal.update(
        db, db_obj=linked_community_deal, obj_in=linked_update
    )
    deal_dict = CommunityDeal.from_orm(community_deal).dict()
    if community_deal_in.feedback == "i_am_interested":
        background_tasks.add_task(
            mail.send_community_deal_feedback,
            deal_dict,
            opposite_community_id=linked_community_deal.id,
            opposite_community_creator_id=linked_community_deal.creator.id,
            create_in_app_notification=create_in_app_notification,
            db=db,
            current_user=current_user,
            read_subscription_func=read_subscriptions_email,
        )


        positions = []
        player_info = community_deal.community_proposal.player_records.player_info
        com_proposal = community_deal.community_proposal

        existing_player = crud.player_record.get_with_wyscout(
            db=db, wyId=player_info.playerId, org_id=current_user.organization_id
        )
        if existing_player:
            return
        
        if player_info.primary_ws_position:
            positions.append(positionMappingWS[player_info.primary_ws_position])
        if player_info.secondary_ws_position:
            if positionMappingWS[player_info.secondary_ws_position] not in positions:
                positions.append(positionMappingWS[player_info.secondary_ws_position])
        player_record = PlayerRecordCreate(
            playerId=player_info.playerId,
            position=positions,
            club_asking_price=com_proposal.club_asking_price,
            expected_net_salary=com_proposal.expected_salary,
        )
        try:
            player = crud.player_record.create_with_user(
                db, obj_in=player_record, user=current_user
            )
            comment = crud.comment.create(
                    db=db,
                    obj_in=CommentCreate(comment=f"Proposed from {com_proposal.creator.email} | {com_proposal.organization.name}", creator=current_user.email, player_id=player.id),
            )
            if com_proposal.description:
                comment = crud.comment.create(
                    db=db,
                    obj_in=CommentCreate(comment=com_proposal.description, creator=current_user.email, player_id=player.id),
                )
                
        except Exception as e:
            print(e)
